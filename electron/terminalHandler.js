const os = require('os');
const pty = require('node-pty');

class TerminalManager {
  constructor() {
    this.terminals = new Map();
    this.nextId = 1;
  }

  createTerminal(mainWindow, cols = 80, rows = 24) {
    const shell = process.platform === 'win32' ? 'powershell.exe' : process.env.SHELL || '/bin/bash';
    const terminalId = this.nextId++;
    
    const ptyProcess = pty.spawn(shell, [], {
      name: 'xterm-color',
      cols,
      rows,
      cwd: process.env.HOME || process.env.USERPROFILE,
      env: process.env
    });

    this.terminals.set(terminalId, ptyProcess);

    // Handle data from terminal
    ptyProcess.onData((data) => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('terminal:data', { terminalId, data });
      }
    });

    // Handle terminal exit
    ptyProcess.onExit(({ exitCode, signal }) => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('terminal:exit', { terminalId, exitCode, signal });
      }
      this.terminals.delete(terminalId);
    });

    return terminalId;
  }

  writeToTerminal(terminalId, data) {
    const terminal = this.terminals.get(terminalId);
    if (terminal) {
      terminal.write(data);
    }
  }

  resizeTerminal(terminalId, cols, rows) {
    const terminal = this.terminals.get(terminalId);
    if (terminal) {
      terminal.resize(cols, rows);
    }
  }

  killTerminal(terminalId) {
    const terminal = this.terminals.get(terminalId);
    if (terminal) {
      terminal.kill();
      this.terminals.delete(terminalId);
    }
  }

  killAllTerminals() {
    this.terminals.forEach((terminal, id) => {
      terminal.kill();
    });
    this.terminals.clear();
  }
}

// Singleton instance
let terminalManager = null;

function initializeTerminalHandlers(ipcMain, mainWindow) {
  terminalManager = new TerminalManager();

  // Create new terminal
  ipcMain.handle('terminal:create', async (event, { cols, rows }) => {
    try {
      const terminalId = terminalManager.createTerminal(mainWindow, cols, rows);
      return { success: true, terminalId };
    } catch (error) {
      console.error('Failed to create terminal:', error);
      return { success: false, error: error.message };
    }
  });

  // Write data to terminal
  ipcMain.handle('terminal:write', async (event, { terminalId, data }) => {
    try {
      terminalManager.writeToTerminal(terminalId, data);
      return { success: true };
    } catch (error) {
      console.error('Failed to write to terminal:', error);
      return { success: false, error: error.message };
    }
  });

  // Resize terminal
  ipcMain.handle('terminal:resize', async (event, { terminalId, cols, rows }) => {
    try {
      terminalManager.resizeTerminal(terminalId, cols, rows);
      return { success: true };
    } catch (error) {
      console.error('Failed to resize terminal:', error);
      return { success: false, error: error.message };
    }
  });

  // Kill terminal
  ipcMain.handle('terminal:kill', async (event, { terminalId }) => {
    try {
      terminalManager.killTerminal(terminalId);
      return { success: true };
    } catch (error) {
      console.error('Failed to kill terminal:', error);
      return { success: false, error: error.message };
    }
  });

  // Kill all terminals on app quit
  const cleanup = () => {
    if (terminalManager) {
      terminalManager.killAllTerminals();
    }
  };

  process.on('exit', cleanup);
  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);

  console.log('Terminal handlers initialized');
}

module.exports = {
  initializeTerminalHandlers
};