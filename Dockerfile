# Multi-stage build for Cerebras Desktop App
FROM node:20-slim AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    git \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libxkbcommon0 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libx11-xcb1 \
    libxcb-dri3-0 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm@10.9.0

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy application files
COPY . .

# Build the application
RUN pnpm build

# Production stage
FROM node:20-slim

# Install runtime dependencies for Electron
RUN apt-get update && apt-get install -y \
    xvfb \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libxkbcommon0 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libx11-xcb1 \
    libxcb-dri3-0 \
    libglib2.0-0 \
    libpangocairo-1.0-0 \
    libcups2 \
    libdbus-1-3 \
    libatspi2.0-0 \
    libx11-6 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrender1 \
    libxcursor1 \
    libxcomposite1 \
    libxdamage1 \
    libxkbcommon0 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    libatk1.0-0 \
    libatspi2.0-0 \
    # Additional dependencies for terminal functionality
    bash \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm globally
RUN npm install -g pnpm@10.9.0

# Create app user
RUN useradd -m -s /bin/bash appuser

# Set working directory
WORKDIR /app

# Copy built application from builder
COPY --from=builder --chown=appuser:appuser /app/dist ./dist
COPY --from=builder --chown=appuser:appuser /app/node_modules ./node_modules
COPY --from=builder --chown=appuser:appuser /app/electron ./electron
COPY --from=builder --chown=appuser:appuser /app/shared ./shared
COPY --from=builder --chown=appuser:appuser /app/package.json ./

# Create directories for electron
RUN mkdir -p /home/<USER>/.config && \
    mkdir -p /home/<USER>/.cache && \
    chown -R appuser:appuser /home/<USER>

# Switch to non-root user
USER appuser

# Set environment variables
ENV DISPLAY=:99
ENV NODE_ENV=production
ENV ELECTRON_DISABLE_SECURITY_WARNINGS=true
ENV ELECTRON_NO_SANDBOX=1

# Expose port for debugging (optional)
EXPOSE 7773

# Start with xvfb-run for virtual display
CMD xvfb-run --server-args="-screen 0 1920x1080x24" npm start