#!/usr/bin/env node

/**
 * Development setup script for ALIAS Portal
 * Handles common development environment issues
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up ALIAS Portal development environment...\n');

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  No .env file found. Creating from .env.example...');
  try {
    const envExample = fs.readFileSync('.env.example', 'utf8');
    // Replace real API keys with placeholders
    const safeEnv = envExample
      .replace(/OPENAI_API_KEY=sk-.*/, 'OPENAI_API_KEY=your_openai_key_here')
      .replace(/ANTHROPIC_API_KEY=sk-ant-.*/, 'ANTHROPIC_API_KEY=your_anthropic_key_here')
      .replace(/CEREBRAS_API_KEY=csk-.*/, 'CEREBRAS_API_KEY=your_cerebras_key_here')
      .replace(/PICA_SECRET_KEY=sk_live_.*/, 'PICA_SECRET_KEY=your_pica_key_here');
    
    fs.writeFileSync('.env', safeEnv);
    console.log('✅ Created .env file with placeholder values');
    console.log('📝 Please edit .env and add your actual API keys\n');
  } catch (error) {
    console.error('❌ Failed to create .env file:', error.message);
  }
}

// Rebuild native modules
console.log('🔧 Rebuilding native modules...');
try {
  execSync('pnpm rebuild node-pty', { stdio: 'inherit' });
  console.log('✅ Native modules rebuilt successfully\n');
} catch (error) {
  console.error('❌ Failed to rebuild native modules:', error.message);
  console.log('💡 Try running: pnpm install --force\n');
}

// Check for TypeScript issues
console.log('🔍 Checking for common issues...');

// Check WidgetContext.tsx syntax
const widgetContextPath = 'src/renderer/widgets/context/WidgetContext.tsx';
if (fs.existsSync(widgetContextPath)) {
  const content = fs.readFileSync(widgetContextPath, 'utf8');
  if (content.includes(');n      if')) {
    console.log('⚠️  Found syntax error in WidgetContext.tsx - fixing...');
    const fixed = content.replace(');n      if', ');\n      if');
    fs.writeFileSync(widgetContextPath, fixed);
    console.log('✅ Fixed WidgetContext.tsx syntax error');
  }
}

console.log('\n🎉 Development environment setup complete!');
console.log('\n📋 Next steps:');
console.log('1. Edit .env file with your API keys');
console.log('2. Run: pnpm dev');
console.log('3. If port 5173 is busy, the app will use 5174');
console.log('\n🔧 Troubleshooting:');
console.log('- If Electron fails to start: pnpm rebuild');
console.log('- If Vite fails: rm -rf node_modules/.vite && pnpm dev');
console.log('- For native module issues: pnpm install --force');
