version: '3.8'

services:
  cerebras-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cerebras-desktop-app
    
    # Port mappings
    ports:
      - "7773:7773"  # Electron debug port
    
    # Load environment from .env file
    env_file:
      - .env
    
    # Additional environment variables
    environment:
      - DISPLAY=${DISPLAY:-:99}
      - NODE_ENV=production
      - ELECTRON_DISABLE_SECURITY_WARNINGS=true
      - ELECTRON_NO_SANDBOX=1
      - ELECTRON_DEBUG_PORT=7773
    
    # Volumes for persistence
    volumes:
      # Application data
      - cerebras-data:/home/<USER>/.config
      - cerebras-cache:/home/<USER>/.cache
      # For X11 forwarding on Linux
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      # Optional: Mount local directory for file access
      # - ./shared-files:/app/shared-files
    
    # Network mode for X11 (Linux)
    network_mode: host
    
    # Security options
    security_opt:
      - seccomp:unconfined
    
    # Device access for GPU acceleration (optional)
    # devices:
    #   - /dev/dri:/dev/dri
    
    # Restart policy
    restart: unless-stopped
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

  # Optional: VNC server for remote access
  vnc-server:
    image: dorowu/ubuntu-desktop-lxde-vnc:latest
    container_name: cerebras-vnc
    ports:
      - "7771:80"     # noVNC web interface
      - "7772:5900"   # VNC port
    environment:
      - VNC_PASSWORD=cerebras123
      - RESOLUTION=1920x1080
      - USER=appuser
      - PASSWORD=appuser
    volumes:
      - cerebras-vnc-data:/home
    restart: unless-stopped
    profiles:
      - vnc

volumes:
  cerebras-data:
    driver: local
  cerebras-cache:
    driver: local
  cerebras-vnc-data:
    driver: local