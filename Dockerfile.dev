# Development Dockerfile with hot reload support
FROM node:20-slim

# Install dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    git \
    xvfb \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libxkbcommon0 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libx11-xcb1 \
    libxcb-dri3-0 \
    libglib2.0-0 \
    libpangocairo-1.0-0 \
    libcups2 \
    libdbus-1-3 \
    bash \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm
RUN npm install -g pnpm@10.9.0

# Create app user
RUN useradd -m -s /bin/bash appuser

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /home/<USER>/.config && \
    mkdir -p /home/<USER>/.cache && \
    chown -R appuser:appuser /home/<USER>
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Set environment variables
ENV DISPLAY=:99
ENV NODE_ENV=development
ENV ELECTRON_DISABLE_SECURITY_WARNINGS=true
ENV ELECTRON_NO_SANDBOX=1

# Copy package files
COPY --chown=appuser:appuser package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install

# Expose ports
EXPOSE 7774 7773

# Start with xvfb-run
CMD xvfb-run --server-args="-screen 0 1920x1080x24" pnpm dev