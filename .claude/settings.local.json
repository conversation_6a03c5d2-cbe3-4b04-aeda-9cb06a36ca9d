{"permissions": {"allow": ["Bash(grep:*)", "Bash(pnpm add:*)", "Bash(find:*)", "<PERSON><PERSON>(touch:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(chmod:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(ln:*)", "Bash(node:*)", "<PERSON><PERSON>(thv search:*)", "<PERSON><PERSON>(git clone:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__filesystem__directory_tree", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__search_files", "Bash(sudo rm:*)", "Bash(pnpm dev:*)", "Bash(cp:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npm:*)", "Bash(npx:*)", "Bash(bash:*)", "<PERSON><PERSON>(sed:*)", "Bash(/Users/<USER>/ace/test-ace-slash.sh:*)", "<PERSON><PERSON>(source:*)", "Bash(sudo ln:*)", "Bash(/Users/<USER>/ace/ace --version)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:www.cuicui.day)", "WebFetch(domain:reaviz.dev)", "WebFetch(domain:v0.dev)", "<PERSON><PERSON>(claude mcp:*)", "Bash(pnpm approve-builds:*)", "Bash(pnpm rebuild:*)", "Bash(./setup-shortcut.sh:*)"], "deny": []}}