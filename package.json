{"name": "alias-portal-app", "version": "1.0.2", "description": "ALIAS Portal – a desktop application integrated with the ALIAS APIs", "main": "electron/main.js", "type": "commonjs", "scripts": {"dev": "concurrently \"pnpm dev:vite\" \"pnpm dev:electron\"", "dev:vite": "vite", "dev:electron": "cross-env NODE_ENV=development electron .", "build": "vite build", "build:electron": "electron-builder", "dist": "pnpm build && pnpm build:electron", "start": "electron .", "docker:build": "docker build -t cerebras-desktop-app .", "docker:build:dev": "docker build -f Dockerfile.dev -t cerebras-desktop-app:dev .", "docker:run": "docker run --rm -it -e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix:rw cerebras-desktop-app", "docker:run:dev": "docker run --rm -it -e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix:rw -v $(pwd):/app -p 7774:5173 -p 7773:7773 cerebras-desktop-app:dev", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f", "docker:vnc": "docker-compose --profile vnc up -d"}, "keywords": ["electron", "alias", "portal", "desktop", "chat"], "author": "", "license": "ISC", "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@cerebras/cerebras_cloud_sdk": "latest", "@heroui/react": "^2.7.10", "@modelcontextprotocol/sdk": "^1.7.0", "@monaco-editor/react": "^4.7.0", "@phosphor-icons/react": "^2.1.10", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "ai": "^4.3.16", "ansi-to-html": "^0.7.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "color2k": "^2.0.3", "date-fns": "^4.1.0", "dexie": "^4.0.11", "dotenv": "^16.5.0", "electron-json-storage": "^4.6.0", "framer-motion": "^12.18.1", "highlight.js": "^11.11.1", "lucide-react": "^0.503.0", "mermaid": "^11.6.0", "node-fetch": "^2.6.6", "prop-types": "^15.8.1", "react": "19.0.0", "react-colorful": "^5.6.1", "react-dom": "19.0.0", "react-markdown": "^10.1.0", "react-router-dom": "7.3.0", "react-syntax-highlighter": "^15.6.1", "reaviz": "^16.0.4", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "systeminformation": "^5.27.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "values.js": "^2.1.1", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/preset-react": "^7.26.3", "@eslint/js": "^9.25.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "concurrently": "9.1.2", "cross-env": "^7.0.3", "electron": "^27.3.11", "electron-builder": "^24.13.3", "electron-is-dev": "^3.0.1", "electron-rebuild": "^3.2.9", "eslint": "^9.25.1", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "vite": "^6.2.6", "wait-on": "8.0.3"}, "build": {"appId": "com.alias.portal", "productName": "ALIAS Portal", "files": ["dist/**/*", "electron/**/*", "shared/**/*", "node_modules/**/*"], "asarUnpack": ["electron/scripts/*.sh"], "directories": {"buildResources": "public", "output": "release"}, "mac": {"category": "public.app-category.productivity"}}, "packageManager": "pnpm@10.9.0+sha512.0486e394640d3c1fb3c9d43d49cf92879ff74f8516959c235308f5a8f62e2e19528a65cdc2a3058f587cde71eba3d5b56327c8c33a97e4c4051ca48a10ca2d5f", "pnpm": {"ignoredBuiltDependencies": ["electron", "esbuild"], "onlyBuiltDependencies": ["electron", "node-pty"]}, "optionalDependencies": {"node-pty": "1.0.0"}}