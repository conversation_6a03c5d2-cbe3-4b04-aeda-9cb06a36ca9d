# MCP Servers Guide for Cerebras Desktop App

## What are MCP Servers?

MCP (Model Context Protocol) servers provide tools that extend the AI's capabilities. Each server offers specific functionalities like file system access, web fetching, database queries, and more.

## Quick Setup

1. Go to **Settings** in the app
2. Find the **MCP Servers** section
3. Use the **Quick Setup** dropdown to add popular servers
4. Click the server you want and configure any required settings
5. The servers will automatically start when you launch the app

## Available MCP Server Presets

### File System
- **ID**: `filesystem`
- **Description**: Read, write, and manage files in your local file system
- **Configuration**: You'll need to specify which directory to give access to

### GitHub
- **ID**: `github`
- **Description**: Interact with GitHub repositories, issues, and pull requests
- **Requirements**: GitHub Personal Access Token

### Memory
- **ID**: `memory`
- **Description**: Store and retrieve information across conversations
- **Use Case**: Perfect for maintaining context between chats

### Time
- **ID**: `time`
- **Description**: Get current time, timezone information, and time calculations
- **Use Case**: Scheduling, time zone conversions, date calculations

### Web Fetch
- **ID**: `fetch`
- **Description**: Fetch and process content from web URLs
- **Use Case**: Reading web pages, downloading content, web scraping

### SQLite
- **ID**: `sqlite`
- **Description**: Query and manage SQLite databases
- **Requirements**: Path to your SQLite database file

### PostgreSQL
- **ID**: `postgres`
- **Description**: Query and manage PostgreSQL databases
- **Requirements**: PostgreSQL connection string

### Puppeteer
- **ID**: `puppeteer`
- **Description**: Automate web browser interactions and screenshots
- **Use Case**: Web automation, taking screenshots, testing

### Kibo UI
- **ID**: `kibo-ui`
- **Description**: Access Kibo UI component documentation and examples
- **Type**: HTTP server (no installation needed)

### Context7
- **ID**: `context7`
- **Description**: Search and retrieve code documentation for various libraries
- **Use Case**: Getting up-to-date documentation while coding

## Using MCP Tools in Chat

1. Once servers are configured and connected, their tools are available in chat
2. When the AI wants to use a tool, you'll see an approval dialog
3. You can approve or deny each tool use
4. Check "Remember this choice" to skip future approvals for the same tool

## Managing Servers

### View Connected Servers
- Click the tools icon in the chat interface
- See all connected servers and their available tools
- View server logs for debugging

### Disconnect/Reconnect
- Use the Tools panel to disconnect servers temporarily
- Disconnected servers can be reconnected without reconfiguration
- Servers marked as disabled won't auto-start

### Edit Configuration
- Go to Settings > MCP Servers
- Click edit on any server to modify its configuration
- Changes take effect after reconnecting the server

## Troubleshooting

### Server Won't Connect
1. Check the server logs in the Tools panel
2. Verify all required configuration (tokens, paths, etc.)
3. Ensure you have internet connection for npx commands
4. Try disconnecting and reconnecting

### Tools Not Working
1. Make sure the server shows as "Connected"
2. Check if tool approval is being blocked
3. Review server logs for errors
4. Verify the server has the expected tools

### Performance Issues
- Disable servers you're not actively using
- Some servers (like puppeteer) use more resources
- Check system resources if multiple servers are running

## Security Notes

- MCP servers have access to resources you configure (files, databases, etc.)
- Always review tool approvals before accepting
- Use specific directories rather than giving full system access
- Keep API tokens and credentials secure
- Servers run with the same permissions as the desktop app

## Advanced Configuration

### Manual Server Addition
1. Use the form view for standard configurations
2. Use JSON view for complex setups
3. Environment variables can be added for API keys
4. Custom arguments can be specified for stdio servers

### Custom MCP Servers
You can add any MCP-compatible server by specifying:
- **stdio**: Command and arguments to run the server
- **SSE**: URL for Server-Sent Events endpoint
- **HTTP**: URL for streamable HTTP endpoint

## Need Help?

- Check server logs for detailed error messages
- Refer to individual server documentation for specific features
- Report issues with the desktop app on GitHub
- MCP protocol documentation: https://modelcontextprotocol.com