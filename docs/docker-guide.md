# Docker Guide for Cerebras Desktop App

This guide explains how to run the Cerebras Desktop App in Docker containers.

## Prerequisites

- Docker installed on your system
- Docker Compose (optional, for easier management)
- X11 server (for GUI display on Linux/macOS)

## Quick Start

### Build and Run with Docker

```bash
# Build the Docker image
pnpm docker:build

# Run the container (Linux/macOS with X11)
pnpm docker:run
```

### Build and Run with Docker Compose

```bash
# Start the application
pnpm docker:compose:up

# View logs
pnpm docker:compose:logs

# Stop the application
pnpm docker:compose:down
```

## Development Mode

For development with hot reload:

```bash
# Build development image
pnpm docker:build:dev

# Run with volume mounting for hot reload
pnpm docker:run:dev

# Or use docker-compose for development
docker-compose -f docker-compose.dev.yml up
```

### Development Ports
- `7774` - Vite dev server (mapped from container's 5173)
- `7773` - Electron debug port

## Remote Access with VNC

To access the app remotely via web browser:

```bash
# Start with VNC server
pnpm docker:vnc

# Access via browser at:
# http://localhost:7771
# VNC password: cerebras123
```

## Port Configuration

The application uses non-conflicting ports in the 777x range:

| Service | Host Port | Container Port | Description |
|---------|-----------|----------------|-------------|
| Electron Debug | 7773 | 7773 | Debugging port for Electron |
| Vite Dev Server | 7774 | 5173 | Development server (dev mode only) |
| VNC Web | 7771 | 80 | noVNC web interface |
| VNC | 7772 | 5900 | VNC protocol port |

## Configuration

### Environment Variables

Docker automatically loads environment variables from the `.env` file in your project root.

1. Copy the example file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your API keys:
   ```env
   # API Keys
   CEREBRAS_API_KEY=your_key_here
   OPENAI_API_KEY=your_key_here
   ANTHROPIC_API_KEY=your_key_here
   GOOGLE_API_KEY=your_key_here
   PICA_SECRET_KEY=your_key_here
   ```

3. For X11 display, uncomment and adjust the DISPLAY variable in `.env`:
   - Linux: `DISPLAY=:0`
   - macOS: `DISPLAY=host.docker.internal:0`
   - Windows: `DISPLAY=host.docker.internal:0.0`

The `docker-compose.yml` and `docker-compose.dev.yml` files are configured to automatically load all variables from your `.env` file using the `env_file` directive.

### Volume Mounts

The docker-compose.yml includes persistent volumes:
- `cerebras-data`: Application configuration
- `cerebras-cache`: Cache files
- `cerebras-vnc-data`: VNC server data (if using VNC)

### Resource Limits

Default resource limits in docker-compose.yml:
- CPU: 2 cores (limit), 1 core (reservation)
- Memory: 4GB (limit), 2GB (reservation)

Adjust these in docker-compose.yml as needed.

## Platform-Specific Setup

### Linux

X11 forwarding should work out of the box:

```bash
# Allow X server connections
xhost +local:docker

# Set DISPLAY in .env file
echo "DISPLAY=:0" >> .env

# Run the container
pnpm docker:run
```

### macOS

Install XQuartz for X11 support:

```bash
# Install XQuartz
brew install --cask xquartz

# Start XQuartz
open -a XQuartz

# In XQuartz preferences, ensure "Allow connections from network clients" is checked

# Allow connections
xhost +localhost

# Set DISPLAY in .env file
echo "DISPLAY=host.docker.internal:0" >> .env

# Run the container
pnpm docker:run
```

### Windows

Use WSL2 with an X server like VcXsrv:

1. Install VcXsrv
2. Start VcXsrv with "Disable access control" checked
3. In WSL2:
   ```bash
   # Set DISPLAY in .env file
   echo "DISPLAY=host.docker.internal:0.0" >> .env
   
   # Run the container
   pnpm docker:run
   ```

## Troubleshooting

### Display Issues

If you see "Cannot open display" errors:

```bash
# Check DISPLAY variable
echo $DISPLAY

# Test X11 connection
xeyes  # Should show eyes following mouse

# For Docker, ensure X11 socket is mounted
ls -la /tmp/.X11-unix/
```

### Permission Issues

If you encounter permission errors:

```bash
# Ensure X server allows connections
xhost +local:docker

# Check file permissions in container
docker exec -it cerebras-desktop-app ls -la /home/<USER>
```

### Performance Issues

For better performance:
- Increase resource limits in docker-compose.yml
- Use GPU acceleration (uncomment devices section in docker-compose.yml)
- Reduce screen resolution in VNC mode

### Debugging

To debug the application:

```bash
# Run container with shell
docker run -it --entrypoint /bin/bash cerebras-desktop-app

# Check logs
docker logs cerebras-desktop-app

# Enable Electron debugging
docker run -e ELECTRON_ENABLE_LOGGING=1 cerebras-desktop-app
```

## Security Considerations

- The container runs as non-root user (appuser)
- Electron security warnings are disabled for container compatibility
- Use secrets management for API keys (don't commit .env file)
- VNC access is password-protected but not encrypted

## Building for Production

For production deployment:

1. Build with specific tags:
   ```bash
   docker build -t cerebras-app:v1.0.2 .
   ```

2. Use Docker secrets for API keys
3. Enable SSL/TLS for VNC access
4. Consider using Kubernetes for orchestration

## Cleanup

To clean up Docker resources:

```bash
# Stop and remove containers
docker-compose down

# Remove volumes (WARNING: deletes data)
docker-compose down -v

# Remove images
docker rmi cerebras-desktop-app

# Clean up all unused resources
docker system prune -a
```