# Desktop Shortcuts for Cerebras Docker

This directory contains scripts to create desktop shortcuts for launching Cerebras Desktop App with <PERSON><PERSON>.

## Quick Setup

Run the setup script to automatically create a desktop shortcut:

```bash
cd shortcuts
./setup-shortcut.sh
```

## Manual Setup

### macOS

1. Double-click `CerebrasDocker.command` to test it works
2. To create a permanent shortcut:
   - Right-click `CerebrasDocker.command`
   - Select "Make Alias"
   - Drag the alias to your Desktop
   - Rename it to "Cerebras Docker"

### Linux

1. Copy the .desktop file to your desktop:
   ```bash
   cp CerebrasDocker.desktop ~/Desktop/
   chmod +x ~/Desktop/CerebrasDocker.desktop
   ```

2. You may need to right-click and select "Allow Launching" on first use

### Windows

1. Create a new shortcut on desktop
2. Set target to:
   ```
   cmd /k "cd /d C:\path\to\cerebras-desktop-app && docker-compose up -d && pause"
   ```
3. Set name to "Cerebras Docker"

## What the Shortcuts Do

The shortcuts will:
1. Check if <PERSON><PERSON> is running
2. Navigate to the project directory
3. Run `docker-compose up -d` to start the containers
4. Show success/error message
5. Wait for keypress before closing

## Customizing

Edit the script files to:
- Add custom environment variables
- Change Docker Compose options
- Add pre-flight checks
- Modify the UI behavior