#!/bin/bash

# Cerebras Desktop App - Docker Launcher
# This script starts the Cerebras Desktop App using Docker Compose

# Change to the app directory
cd "$(dirname "$0")/.." || exit 1

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: <PERSON><PERSON> is not running. Please start Docker Desktop first."
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

echo "Starting Cerebras Desktop App with Docker..."
echo "========================================"

# Start the containers
docker-compose up -d

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Cerebras Desktop App started successfully!"
    echo ""
    echo "The app is running in the background."
    echo "To view logs: docker-compose logs -f"
    echo "To stop: docker-compose down"
    echo ""
    echo "Press any key to close this window..."
else
    echo ""
    echo "❌ Failed to start the application."
    echo "Please check the error messages above."
    echo ""
    echo "Press any key to close this window..."
fi

read -n 1