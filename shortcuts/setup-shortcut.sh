#!/bin/bash

# Setup script to create desktop shortcut for Cerebras Docker

echo "Setting up Cerebras Docker desktop shortcut..."

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Detect the operating system
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "Detected macOS"
    
    # Create an AppleScript app
    DESKTOP_PATH="$HOME/Desktop"
    APP_NAME="Cerebras Docker.app"
    APP_PATH="$DESKTOP_PATH/$APP_NAME"
    
    # Create the app bundle structure
    mkdir -p "$APP_PATH/Contents/MacOS"
    mkdir -p "$APP_PATH/Contents/Resources"
    
    # Create the executable script
    cat > "$APP_PATH/Contents/MacOS/CerebrasDocker" << EOF
#!/bin/bash
cd "$PROJECT_DIR"
osascript -e 'tell app "Terminal" to do script "cd \"$PROJECT_DIR\" && docker-compose up -d && echo \"\" && echo \"✅ Cerebras Docker started! Press any key to close...\" && read -n 1"'
EOF
    
    chmod +x "$APP_PATH/Contents/MacOS/CerebrasDocker"
    
    # Create Info.plist
    cat > "$APP_PATH/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>CerebrasDocker</string>
    <key>CFBundleIconFile</key>
    <string>icon</string>
    <key>CFBundleIdentifier</key>
    <string>com.cerebras.docker.launcher</string>
    <key>CFBundleName</key>
    <string>Cerebras Docker</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
</dict>
</plist>
EOF
    
    echo "✅ Created macOS app at: $APP_PATH"
    echo "   You can now double-click it to start Cerebras with Docker!"
    
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "Detected Linux"
    
    DESKTOP_PATH="$HOME/Desktop"
    DESKTOP_FILE="$DESKTOP_PATH/CerebrasDocker.desktop"
    
    # Create desktop file with absolute paths
    cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Cerebras Docker
Comment=Start Cerebras Desktop App with Docker
Exec=gnome-terminal -- bash -c "cd '$PROJECT_DIR' && docker-compose up -d && echo '' && echo '✅ Cerebras Docker started! Press any key to close...' && read -n 1"
Icon=docker
Terminal=true
Categories=Development;Utility;
Path=$PROJECT_DIR
EOF
    
    # Make it executable
    chmod +x "$DESKTOP_FILE"
    
    # Also install to applications menu
    APPS_DIR="$HOME/.local/share/applications"
    mkdir -p "$APPS_DIR"
    cp "$DESKTOP_FILE" "$APPS_DIR/"
    
    echo "✅ Created Linux desktop shortcut at: $DESKTOP_FILE"
    echo "   Also added to applications menu"
    
else
    echo "❌ Unsupported operating system: $OSTYPE"
    exit 1
fi

echo ""
echo "Setup complete! You can now use the desktop shortcut to start Cerebras with Docker."