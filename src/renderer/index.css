@import './themes/cuicui-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 270 67% 58%; /* Cuicui Purple */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 328 85% 53%; /* Cuicui Pink */
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 270 67% 58%; /* <PERSON><PERSON><PERSON><PERSON> Purple */

    --radius: 0.75rem;
    --blur: 12px;
    --shadow-color: 0 0% 0%;
    --shadow-opacity: 0.1;
  }

  .dark {
    --background: 0 0% 0%; /* Pure black */
    --foreground: 0 0% 98%;

    --card: 0 0% 0%; /* Pure black */
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 0%; /* Pure black */
    --popover-foreground: 0 0% 98%;

    --primary: 270 67% 58%; /* Cuicui Purple */
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 8%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 8%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 328 85% 53%; /* Cuicui Pink */
    --accent-foreground: 0 0% 98%;

    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 10%; /* Very subtle borders */
    --input: 0 0% 10%;
    --ring: 270 67% 58%; /* Cuicui Purple */
    
    --blur: 20px;
    --shadow-color: 0 0% 0%;
    --shadow-opacity: 0.3;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--reaviz-font-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
  }
}

/* Custom scrollbar styles for chat area */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

/* Dark mode scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

:root {
  --color-radix-slate-dark-1: 240 6% 7%;
  --color-radix-slate-dark-2: 220 6% 10%;
  --color-radix-slate-dark-3: 225 6% 14%;
  --color-radix-slate-dark-4: 210 7% 16%;
  --color-radix-slate-dark-5: 214 7% 19%;
  --color-radix-slate-dark-6: 213 8% 23%;
  --color-radix-slate-dark-7: 213 8% 28%;
  --color-radix-slate-dark-8: 212 8% 38%;
  --color-radix-slate-dark-9: 219 6% 44%;
  --color-radix-slate-dark-10: 222 5% 49%;
  --color-radix-slate-dark-11: 216 7% 71%;
  --color-radix-slate-dark-12: 220 9% 94%;
  --color-radix-yellow-dark-1: 47 29% 6%;
  --color-radix-yellow-dark-2: 45 29% 8%;
  --color-radix-yellow-dark-3: 45 80% 10%;
  --color-radix-yellow-dark-4: 48 100% 11%;
  --color-radix-yellow-dark-5: 47 100% 13%;
  --color-radix-yellow-dark-6: 48 95% 16%;
  --color-radix-yellow-dark-7: 46 63% 25%;
  --color-radix-yellow-dark-8: 45 60% 32%;
  --color-radix-yellow-dark-9: 53 100% 58%;
  --color-radix-yellow-dark-10: 60 100% 67%;
  --color-radix-yellow-dark-11: 53 90% 62%;
  --color-radix-yellow-dark-12: 53 79% 84%;
  --color-radix-red-dark-1: 0 19% 8%;
  --color-radix-red-dark-2: 355 25% 10%;
  --color-radix-red-dark-3: 350 53% 15%;
  --color-radix-red-dark-4: 348 68% 19%;
  --color-radix-red-dark-5: 350 63% 23%;
  --color-radix-red-dark-6: 352 53% 29%;
  --color-radix-red-dark-7: 355 47% 37%;
  --color-radix-red-dark-8: 358 45% 49%;
  --color-radix-red-dark-9: 358 75% 59%;
  --color-radix-red-dark-10: 360 79% 65%;
  --color-radix-red-dark-11: 2 100% 79%;
  --color-radix-red-dark-12: 350 100% 91%;
  --color-radix-green-dark-1: 154 20% 7%;
  --color-radix-green-dark-2: 153 20% 9%;
  --color-radix-green-dark-3: 152 41% 13%;
  --color-radix-green-dark-4: 154 55% 15%;
  --color-radix-green-dark-5: 154 52% 19%;
  --color-radix-green-dark-6: 153 46% 23%;
  --color-radix-green-dark-7: 152 44% 28%;
  --color-radix-green-dark-8: 151 45% 34%;
  --color-radix-green-dark-9: 151 55% 42%;
  --color-radix-green-dark-10: 151 55% 45%;
  --color-radix-green-dark-11: 151 65% 54%;
  --color-radix-green-dark-12: 144 70% 82%;
  --color-cerebras-orange: 15 88% 55%;
  --color-cerebras-orange-hover: 22 100% 68%;
  --color-cerebras-burgundy: 5 75% 17%;
  --color-cerebras-teal: 184 43% 42%;
  --color-cerebras-blue-light: 166 15% 81%;
  --color-cerebras-black-900: 0 0% 10%;
  --color-cerebras-black-850: 30 1% 15%;
  --color-cerebras-black-800: 30 1% 20%;
  --color-cerebras-black-700: 30 1% 30%;
  --color-cerebras-black-500: 30 1% 50%;
  --color-cerebras-black-300: 30 2% 69%;
  --color-cerebras-black-200: 30 4% 79%;
  --color-cerebras-black-100: 30 17% 91%;
  --color-cerebras-black-50: 30 24% 96%;
  --color-cerebras-purple: 266 67% 72%;
  --color-cerebras-babyBlue: 190 49% 67%;
  --color-cerebras-mandarin: 22 100% 68%;
  --color-cerebras-green: 83 81% 55%;
  --color-white: 0 0% 100%;
  --color-black: 0 0% 0%;
}

body {
  margin: 0;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #000000; /* Pure black */
  color: hsl(var(--foreground));
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Remove gradient overlays for pure black theme */

/* Animation removed for pure black theme */

@layer components {
  .glass {
    background: #000000; /* Pure black */
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
  }

  .glass-subtle {
    background: #000000; /* Pure black */
    border: 1px solid rgba(255, 255, 255, 0.03);
  }

  .btn {
    @apply px-4 py-2.5 rounded-lg font-medium transition-all duration-200 relative overflow-hidden;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
  }

  .btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .btn-primary {
    background: linear-gradient(135deg, hsl(270 67% 58%) 0%, hsl(328 85% 53%) 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 15px 0 hsl(270 67% 58% / 0.4);
    position: relative;
    overflow: hidden;
  }
  
  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px 0 hsl(270 67% 58% / 0.6);
  }
  
  .btn-primary:hover::before {
    left: 100%;
  }

  .input {
    @apply px-4 py-3 rounded-lg transition-all duration-200;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: hsl(var(--foreground));
    font-size: 0.95rem;
  }

  .input:focus {
    outline: none;
    border-color: hsl(270 67% 58% / 0.5);
    background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 0 0 3px hsl(270 67% 58% / 0.1);
  }

  .input::placeholder {
    color: hsl(var(--muted-foreground));
  }

  /* Markdown styling */
  .markdown-content {
    @apply text-inherit;
    line-height: 1.7;
  }

  .markdown-content p:not(:last-child) {
    @apply mb-3;
  }

  .markdown-content h1 {
    @apply text-3xl font-bold my-4;
    background: linear-gradient(135deg, hsl(var(--foreground)), hsl(var(--muted-foreground)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .markdown-content h2 {
    @apply text-2xl font-bold my-3;
  }

  .markdown-content h3 {
    @apply text-xl font-semibold my-2;
  }

  .markdown-content ul,
  .markdown-content ol {
    @apply pl-6 my-2;
  }

  .markdown-content ul {
    @apply list-disc;
  }

  .markdown-content ol {
    @apply list-decimal;
  }

  .markdown-content code {
    @apply px-1.5 py-0.5 rounded-md text-sm;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
  }

  .markdown-content pre {
    @apply p-4 rounded-lg my-3 overflow-auto;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
  }

  .markdown-content pre code {
    @apply bg-transparent p-0 border-0;
  }

  .markdown-content blockquote {
    @apply border-l-4 pl-4 italic my-3;
    border-color: hsl(var(--color-cerebras-orange) / 0.5);
    background: linear-gradient(90deg, hsl(var(--color-cerebras-orange) / 0.1), transparent);
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .markdown-content table {
    @apply border-collapse my-3 w-full;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .markdown-content th,
  .markdown-content td {
    @apply px-3 py-2;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .markdown-content th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
  }
}

/* Apply link styles outside the layer for higher specificity */
.markdown-content a {
  @apply no-underline relative;
  color: hsl(var(--color-cerebras-orange));
  transition: all 0.2s ease;
  word-break: break-all;
}

.markdown-content a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, hsl(var(--color-cerebras-orange)), hsl(var(--color-cerebras-orange-hover)));
  transition: width 0.3s ease;
}

.markdown-content a:hover {
  color: hsl(var(--color-cerebras-orange-hover));
}

.markdown-content a:hover::after {
  width: 100%;
}

/* Add these styles at the end of index.css */

.tools-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tools-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: hsl(var(--color-cerebras-orange));
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  padding: 8px;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tools-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, hsl(var(--color-cerebras-orange) / 0.3), transparent);
  transform: translate(-50%, -50%);
  transition: width 0.5s, height 0.5s;
}

.tools-button:hover {
  color: hsl(var(--color-white));
  background: hsl(var(--color-cerebras-orange));
  transform: translateY(-2px);
  box-shadow: 0 5px 15px hsl(var(--color-cerebras-orange) / 0.4);
}

.tools-button:hover::before {
  width: 300%;
  height: 300%;
}

.tools-button:active {
  transform: translateY(0);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: hsl(var(--color-cerebras-orange));
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 500;
  padding: 6px 14px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.status-indicator.loading {
  color: hsl(var(--color-cerebras-babyBlue));
  background: linear-gradient(135deg, rgba(119, 170, 255, 0.1), rgba(119, 170, 255, 0.05));
  border-color: rgba(119, 170, 255, 0.2);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-top-color: hsl(var(--color-cerebras-orange));
  border-right-color: hsl(var(--color-cerebras-orange));
  border-radius: 50%;
  animation: spin 0.8s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  filter: drop-shadow(0 0 2px hsl(var(--color-cerebras-orange) / 0.5));
}

.refresh-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  color: hsl(var(--color-cerebras-orange));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  backdrop-filter: blur(10px);
}

.refresh-button:hover {
  color: hsl(var(--color-white));
  background: linear-gradient(135deg, hsl(var(--color-cerebras-orange)), hsl(var(--color-cerebras-orange-hover)));
  border-color: hsl(var(--color-cerebras-orange));
  box-shadow: 0 4px 12px hsl(var(--color-cerebras-orange) / 0.4);
}

.refresh-button span {
  display: inline-block;
  transition: transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.refresh-button:hover span {
  transform: rotate(180deg);
}

.refresh-button:active {
  transform: scale(0.95);
}

/* Streaming message animation */
.streaming-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.streaming-indicator .dot-1,
.streaming-indicator .dot-2,
.streaming-indicator .dot-3 {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  /* Apply dark background directly */
  background-color: #ccc;
  opacity: 0.6;
}

/* Remove .dark specific streaming indicator styles */
/*
.dark .streaming-indicator .dot-1,
.dark .streaming-indicator .dot-2,
.dark .streaming-indicator .dot-3 {
  background-color: #ccc;
}
*/

.streaming-indicator .dot-1 {
  animation: pulse-dot 1.4s infinite ease-in-out;
  animation-delay: 0s;
}

.streaming-indicator .dot-2 {
  animation: pulse-dot 1.4s infinite ease-in-out;
  animation-delay: 0.2s;
}

.streaming-indicator .dot-3 {
  animation: pulse-dot 1.4s infinite ease-in-out;
  animation-delay: 0.4s;
}

@keyframes pulse-dot {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }

  50% {
    transform: scale(1.2);
    opacity: 1;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Reasoning section styles */
.reasoning-section {
  margin-top: 12px;
  font-size: 0.9rem;
}

.reasoning-toggle {
  font-size: 0.8rem;
  color: hsl(var(--muted-foreground));
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  text-decoration: none;
  cursor: pointer;
  padding: 4px 12px;
  margin-top: 6px;
  border-radius: 16px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.reasoning-toggle::before {
  content: '▶';
  font-size: 0.7rem;
  transition: transform 0.3s ease;
}

.reasoning-toggle.expanded::before {
  transform: rotate(90deg);
}

.reasoning-toggle:hover {
  color: hsl(var(--color-cerebras-babyBlue));
  background: rgba(119, 170, 255, 0.1);
  border-color: rgba(119, 170, 255, 0.2);
  transform: translateX(2px);
}

.reasoning-content {
  margin-top: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.01));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  font-size: 0.85rem;
  white-space: pre-wrap;
  line-height: 1.6;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Model selector styling */
select#model-select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%23F97316' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-color: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: hsl(var(--foreground));
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

select#model-select:focus {
  outline: none;
  border-color: hsl(var(--color-cerebras-orange) / 0.5);
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 0 0 3px hsl(var(--color-cerebras-orange) / 0.1);
}

select#model-select:hover {
  border-color: hsl(var(--color-cerebras-orange) / 0.3);
  background-color: rgba(255, 255, 255, 0.05);
}

select#model-select option {
  background-color: hsl(240 10% 3.9%);
  color: hsl(var(--foreground));
  padding: 8px;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.5);
}

/* Smooth scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #000000;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Focus visible improvements */
:focus-visible {
  outline: 2px solid hsl(var(--color-cerebras-orange));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Improve transitions globally */
* {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Cuicui-inspired animations */
@keyframes cuicui-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cuicui-scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes cuicui-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: cuicui-fade-in 0.5s ease-out;
}

.animate-scale-in {
  animation: cuicui-scale-in 0.3s ease-out;
}

.animate-slide-in {
  animation: cuicui-slide-in 0.4s ease-out;
}

/* Cuicui gradient hover effects */
.cuicui-hover-gradient {
  position: relative;
  overflow: hidden;
}

.cuicui-hover-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, hsl(270 67% 58% / 0.2), hsl(328 85% 53% / 0.2));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cuicui-hover-gradient:hover::before {
  opacity: 1;
}

/* Update gradient text colors throughout app */
.gradient-text-purple-pink {
  background: linear-gradient(135deg, hsl(270 67% 58%), hsl(328 85% 53%));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}