import React, { useState } from 'react';
import Terminal from '../components/Terminal';
import { Button } from '../components/ui/button';
import { Plus, Terminal as TerminalIcon } from 'lucide-react';

const TerminalPage = () => {
  const [terminals, setTerminals] = useState([{ id: 1, isFullscreen: false }]);
  const [nextId, setNextId] = useState(2);

  const addTerminal = () => {
    setTerminals([...terminals, { id: nextId, isFullscreen: false }]);
    setNextId(nextId + 1);
  };

  const removeTerminal = (id) => {
    setTerminals(terminals.filter(t => t.id !== id));
  };

  const toggleFullscreen = (id) => {
    setTerminals(terminals.map(t => 
      t.id === id ? { ...t, isFullscreen: !t.isFullscreen } : t
    ));
  };

  return (
    <div className="h-full w-full p-4 overflow-auto bg-background">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <TerminalIcon className="w-7 h-7" />
            Terminal
          </h2>
          <p className="text-muted-foreground mt-1">
            Access the command line directly from ALIAS Portal
          </p>
        </div>
        <Button onClick={addTerminal} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          New Terminal
        </Button>
      </div>

      {/* Terminals Grid */}
      {terminals.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-96 border-2 border-dashed border-gray-700 rounded-lg">
          <TerminalIcon className="w-16 h-16 text-gray-600 mb-4" />
          <p className="text-gray-500 text-lg mb-4">No terminals open</p>
          <Button onClick={addTerminal} variant="outline">
            Open Terminal
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {terminals.map(terminal => (
            <div key={terminal.id} className={terminal.isFullscreen ? '' : 'relative'}>
              <Terminal
                onClose={() => removeTerminal(terminal.id)}
                isFullscreen={terminal.isFullscreen}
                onToggleFullscreen={() => toggleFullscreen(terminal.id)}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TerminalPage;