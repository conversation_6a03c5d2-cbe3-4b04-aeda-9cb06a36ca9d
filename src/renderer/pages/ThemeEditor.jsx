import React, { useState, useEffect, useRef } from 'react';
import { 
  Palette, Save, Download, RefreshCw, Copy, Check,
  <PERSON>, <PERSON>, Un<PERSON>, <PERSON>o, Share2, Import
} from 'lucide-react';
import ComponentPreview from '../components/theme-editor/ComponentPreview';
import ThemeControlsPanel from '../components/theme-editor/ThemeControlsPanel';

const ThemeEditor = () => {
  const [currentMode, setCurrentMode] = useState('dark');
  const [activeComponent, setActiveComponent] = useState('button');
  const [copied, setCopied] = useState(false);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [controlsExpanded, setControlsExpanded] = useState(true);
  
  // Complete theme state with all TweakCN variables
  const [themeStyles, setThemeStyles] = useState({
    light: {
      // Primary Colors
      '--primary': '222 47% 11%',
      '--primary-foreground': '210 40% 98%',
      
      // Secondary Colors
      '--secondary': '210 40% 96%',
      '--secondary-foreground': '222 47% 11%',
      
      // Accent Colors
      '--accent': '210 40% 96%',
      '--accent-foreground': '222 47% 11%',
      
      // Base Colors
      '--background': '0 0% 100%',
      '--foreground': '222 47% 11%',
      
      // Card Colors
      '--card': '0 0% 100%',
      '--card-foreground': '222 47% 11%',
      
      // Popover Colors
      '--popover': '0 0% 100%',
      '--popover-foreground': '222 47% 11%',
      
      // Muted Colors
      '--muted': '210 40% 96%',
      '--muted-foreground': '215 16% 47%',
      
      // Destructive Colors
      '--destructive': '0 84% 60%',
      '--destructive-foreground': '210 40% 98%',
      
      // Border & Input Colors
      '--border': '214 32% 91%',
      '--input': '214 32% 91%',
      '--ring': '222 47% 11%',
      
      // Chart Colors
      '--chart-1': '12 76% 61%',
      '--chart-2': '173 58% 39%',
      '--chart-3': '197 37% 24%',
      '--chart-4': '43 74% 66%',
      '--chart-5': '27 87% 67%',
      
      // Sidebar Colors
      '--sidebar': '0 0% 98%',
      '--sidebar-foreground': '220 16% 22%',
      '--sidebar-primary': '220 65% 50%',
      '--sidebar-primary-foreground': '220 100% 98%',
      '--sidebar-accent': '220 10% 92%',
      '--sidebar-accent-foreground': '220 16% 22%',
      '--sidebar-border': '220 13% 90%',
      '--sidebar-ring': '220 65% 50%',
      
      // Shadow
      '--shadow-color': '0 0% 0%',
      
      // Typography
      '--font-sans': 'Inter, system-ui, sans-serif',
      '--font-serif': 'Georgia, serif',
      '--font-mono': 'Menlo, monospace',
      '--letter-spacing': '0',
      
      // Layout
      '--radius': '0.5rem',
      '--spacing': '0.25rem',
      
      // Shadow Properties
      '--shadow-opacity': '0.1',
      '--shadow-blur': '10px',
      '--shadow-spread': '0px',
      '--shadow-x': '0px',
      '--shadow-y': '1px',
    },
    dark: {
      // Primary Colors
      '--primary': '270 67% 58%',
      '--primary-foreground': '0 0% 98%',
      
      // Secondary Colors
      '--secondary': '0 0% 8%',
      '--secondary-foreground': '0 0% 98%',
      
      // Accent Colors
      '--accent': '328 85% 53%',
      '--accent-foreground': '0 0% 98%',
      
      // Base Colors
      '--background': '0 0% 0%',
      '--foreground': '0 0% 98%',
      
      // Card Colors
      '--card': '0 0% 0%',
      '--card-foreground': '0 0% 98%',
      
      // Popover Colors
      '--popover': '0 0% 0%',
      '--popover-foreground': '0 0% 98%',
      
      // Muted Colors
      '--muted': '0 0% 8%',
      '--muted-foreground': '0 0% 64%',
      
      // Destructive Colors
      '--destructive': '0 72% 51%',
      '--destructive-foreground': '0 0% 98%',
      
      // Border & Input Colors
      '--border': '0 0% 10%',
      '--input': '0 0% 10%',
      '--ring': '270 67% 58%',
      
      // Chart Colors
      '--chart-1': '220 70% 50%',
      '--chart-2': '160 60% 45%',
      '--chart-3': '30 80% 55%',
      '--chart-4': '280 65% 60%',
      '--chart-5': '340 75% 55%',
      
      // Sidebar Colors
      '--sidebar': '0 0% 5%',
      '--sidebar-foreground': '0 0% 92%',
      '--sidebar-primary': '270 67% 58%',
      '--sidebar-primary-foreground': '0 0% 98%',
      '--sidebar-accent': '0 0% 12%',
      '--sidebar-accent-foreground': '0 0% 92%',
      '--sidebar-border': '0 0% 15%',
      '--sidebar-ring': '270 67% 58%',
      
      // Shadow
      '--shadow-color': '0 0% 0%',
      
      // Typography
      '--font-sans': 'Inter, system-ui, sans-serif',
      '--font-serif': 'Georgia, serif',
      '--font-mono': 'Menlo, monospace',
      '--letter-spacing': '0',
      
      // Layout
      '--radius': '0.5rem',
      '--spacing': '0.25rem',
      
      // Shadow Properties
      '--shadow-opacity': '0.2',
      '--shadow-blur': '10px',
      '--shadow-spread': '0px',
      '--shadow-x': '0px',
      '--shadow-y': '1px',
    }
  });

  // HSL Adjustments
  const [hslAdjustments, setHslAdjustments] = useState({
    hueShift: 0,
    saturationScale: 1,
    lightnessScale: 1,
  });

  // Available fonts
  const availableFonts = [
    'Inter, system-ui, sans-serif',
    'Roboto, sans-serif',
    'Open Sans, sans-serif',
    'Lato, sans-serif',
    'Montserrat, sans-serif',
    'Poppins, sans-serif',
    'Georgia, serif',
    'Times New Roman, serif',
    'Playfair Display, serif',
    'Menlo, monospace',
    'Monaco, monospace',
    'Fira Code, monospace',
    'JetBrains Mono, monospace',
  ];

  // HSL Presets
  const hslPresets = [
    { hue: 0, sat: 1, light: 1 },
    { hue: 30, sat: 1.2, light: 1.1 },
    { hue: 60, sat: 0.8, light: 1.2 },
    { hue: 90, sat: 1.1, light: 0.9 },
    { hue: 120, sat: 1.3, light: 1 },
    { hue: 150, sat: 0.9, light: 1.1 },
    { hue: 180, sat: 1, light: 0.8 },
    { hue: 210, sat: 1.2, light: 1 },
    { hue: 240, sat: 0.7, light: 0.9 },
    { hue: 270, sat: 1.1, light: 1.1 },
    { hue: 300, sat: 1, light: 1.2 },
    { hue: 330, sat: 1.2, light: 0.9 },
    { hue: -30, sat: 0.8, light: 1 },
    { hue: -60, sat: 1, light: 0.8 },
    { hue: -90, sat: 1.1, light: 1.1 },
    { hue: -120, sat: 0.9, light: 1 },
  ];

  const previewRef = useRef(null);


  // Update history when theme changes
  useEffect(() => {
    if (historyIndex === -1 || JSON.stringify(history[historyIndex]) !== JSON.stringify({ themeStyles, hslAdjustments })) {
      const newHistory = [...history.slice(0, historyIndex + 1), { themeStyles: JSON.parse(JSON.stringify(themeStyles)), hslAdjustments: { ...hslAdjustments } }];
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  }, [themeStyles, hslAdjustments]);

  // Apply theme to preview
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply CSS variables
    const styles = themeStyles[currentMode];
    Object.entries(styles).forEach(([key, value]) => {
      if (key.startsWith('--font')) {
        root.style.setProperty(key, value);
      } else if (!key.includes('font') && !key.includes('radius') && !key.includes('spacing') && !key.includes('shadow') && !key.includes('letter')) {
        // Apply HSL adjustments to color variables
        const [h, s, l] = value.split(' ').map((v, i) => 
          i === 0 ? parseInt(v) : parseInt(v) / 100
        );
        
        const adjustedH = (h + hslAdjustments.hueShift + 360) % 360;
        const adjustedS = Math.min(100, Math.max(0, s * hslAdjustments.saturationScale * 100));
        const adjustedL = Math.min(100, Math.max(0, l * hslAdjustments.lightnessScale * 100));
        
        root.style.setProperty(key, `${adjustedH} ${adjustedS}% ${adjustedL}%`);
      } else {
        root.style.setProperty(key, value);
      }
    });
  }, [themeStyles, currentMode, hslAdjustments]);

  // Stream Deck controller mapping
  useEffect(() => {
    const handleKeyPress = (e) => {
      switch(e.key) {
        case 'F14': setCurrentMode(currentMode === 'dark' ? 'light' : 'dark'); break;
        case 'F15': cycleHSLPreset(); break;
        case 'F16': saveTheme(); break;
        case 'F17': exportTheme(); break;
        case 'F18': resetTheme(); break;
        case 'F19': undo(); break;
        case 'F20': redo(); break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentMode]);

  const updateThemeVariable = (varName, value) => {
    setThemeStyles(prev => ({
      ...prev,
      [currentMode]: {
        ...prev[currentMode],
        [varName]: value
      }
    }));
  };

  const cycleHSLPreset = () => {
    const currentIndex = hslPresets.findIndex(p => 
      p.hue === hslAdjustments.hueShift && 
      p.sat === hslAdjustments.saturationScale && 
      p.light === hslAdjustments.lightnessScale
    );
    const nextIndex = (currentIndex + 1) % hslPresets.length;
    const preset = hslPresets[nextIndex];
    setHslAdjustments({
      hueShift: preset.hue,
      saturationScale: preset.sat,
      lightnessScale: preset.light,
    });
  };

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      const state = history[historyIndex - 1];
      setThemeStyles(state.themeStyles);
      setHslAdjustments(state.hslAdjustments);
    }
  };

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      const state = history[historyIndex + 1];
      setThemeStyles(state.themeStyles);
      setHslAdjustments(state.hslAdjustments);
    }
  };

  const saveTheme = async () => {
    const themeData = {
      name: `ALIAS Theme ${new Date().toISOString()}`,
      mode: currentMode,
      styles: themeStyles,
      hslAdjustments,
      timestamp: Date.now()
    };
    
    const savedThemes = JSON.parse(localStorage.getItem('aliasThemes') || '[]');
    savedThemes.push(themeData);
    localStorage.setItem('aliasThemes', JSON.stringify(savedThemes));
    
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const exportTheme = () => {
    const styles = themeStyles[currentMode];
    const cssVars = Object.entries(styles)
      .map(([key, value]) => `  ${key}: ${value};`)
      .join('\n');
    
    const cssContent = `:root {\n${cssVars}\n}\n\n.${currentMode} {\n${cssVars}\n}`;
    
    const blob = new Blob([cssContent], { type: 'text/css' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `alias-theme-${currentMode}-${Date.now()}.css`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const resetTheme = () => {
    window.location.reload();
  };

  const copyCSS = async () => {
    const styles = themeStyles[currentMode];
    const cssVars = Object.entries(styles)
      .map(([key, value]) => `  ${key}: ${value};`)
      .join('\n');
    
    const cssContent = `:root {\n${cssVars}\n}`;
    
    await navigator.clipboard.writeText(cssContent);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };


  return (
    <div className="flex flex-col h-full bg-background relative">
      {/* Header */}
      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Palette className="w-6 h-6 text-purple-500" />
            ALIAS HQ Theme Editor
          </h1>
          
          {/* Action Bar */}
          <div className="flex items-center gap-2">
            {/* History Controls */}
            <button
              onClick={undo}
              disabled={historyIndex <= 0}
              className="p-2 rounded-lg hover:bg-accent/20 transition-colors disabled:opacity-50"
              title="Undo"
            >
              <Undo size={20} />
            </button>
            <button
              onClick={redo}
              disabled={historyIndex >= history.length - 1}
              className="p-2 rounded-lg hover:bg-accent/20 transition-colors disabled:opacity-50"
              title="Redo"
            >
              <Redo size={20} />
            </button>
            
            <div className="w-px h-8 bg-border" />
            
            {/* Actions */}
            <button
              onClick={resetTheme}
              className="p-2 rounded-lg hover:bg-accent/20 transition-colors"
              title="Reset"
            >
              <RefreshCw size={20} />
            </button>
            <button
              onClick={saveTheme}
              className="p-2 rounded-lg hover:bg-accent/20 transition-colors"
              title="Save"
            >
              <Save size={20} />
            </button>
            <button
              onClick={exportTheme}
              className="p-2 rounded-lg hover:bg-accent/20 transition-colors"
              title="Export"
            >
              <Download size={20} />
            </button>
            <button
              onClick={copyCSS}
              className="p-2 rounded-lg hover:bg-accent/20 transition-colors"
              title="Copy CSS"
            >
              {copied ? <Check size={20} /> : <Copy size={20} />}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content - Full Width Preview */}
      <div className="flex-1 overflow-hidden bg-muted/5">
        <div className="h-full overflow-y-auto custom-scrollbar" ref={previewRef}>
          <ComponentPreview currentMode={currentMode} activeComponent={activeComponent} />
        </div>
      </div>

      {/* Bottom Controls Panel */}
      <ThemeControlsPanel
        currentMode={currentMode}
        setCurrentMode={setCurrentMode}
        themeStyles={themeStyles}
        updateThemeVariable={updateThemeVariable}
        hslAdjustments={hslAdjustments}
        setHslAdjustments={setHslAdjustments}
        isExpanded={controlsExpanded}
        setIsExpanded={setControlsExpanded}
      />

    </div>
  );
};

export default ThemeEditor;