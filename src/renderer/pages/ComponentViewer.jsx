import React, { useState, useEffect } from 'react';
import { Search, Copy, Check, Code, Eye, Layers, Package, Sparkles, MessageSquare, BarChart, FileText, Layout, Square, Zap, Blocks, Grid3x3, Sparkle, Users, MessageCircle, Navigation, LayoutDashboard } from 'lucide-react';
import '../components/animations.css';
import { kiboComponents, kiboCategories } from '../lib/kibo-ui-registry';
import { mvpBlocksComponents, mvpBlocksCategories } from '../lib/mvpblocks-registry';
import { componentExamples } from '../lib/kibo-component-loader';
import SimpleKiboComponent from '../lib/simple-kibo-loader';

const ComponentViewer = () => {
  const [components, setComponents] = useState([]);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLibrary, setSelectedLibrary] = useState('kibo'); // 'kibo' or 'mvpblocks'
  const [copiedCode, setCopiedCode] = useState(null);
  const [viewMode, setViewMode] = useState('preview'); // 'preview' or 'code'
  const [loading, setLoading] = useState(true);

  // Icon mapping for categories
  const categoryIcons = {
    'all': Package,
    'ai': Sparkles,
    'feedback': MessageSquare,
    'data-display': BarChart,
    'forms': FileText,
    'layout': Layout,
    'overlay': Square,
    'interactive': Zap,
    // MVP Blocks categories
    'basics': Blocks,
    'cards': Square,
    'chatbot-ui': MessageCircle,
    'creative': Sparkle,
    'grids': Grid3x3,
    'hero': Layout,
    'pricing': BarChart,
    'team': Users,w
    'testimonials': MessageSquare,
    'text-animations': Zap,
    'navigation': Navigation,
    'dashboards': LayoutDashboard
  };

  // Categories for filtering with icons
  const categories = selectedLibrary === 'kibo' 
    ? kiboCategories.map(cat => ({
        ...cat,
        icon: categoryIcons[cat.id] || Layers
      }))
    : mvpBlocksCategories.map(cat => ({
        ...cat,
        icon: categoryIcons[cat.id] || Layers
      }));

  // Load components from Kibo UI registry

  useEffect(() => {
    // Load components from registry based on selected library
    if (selectedLibrary === 'kibo') {
      setComponents(kiboComponents);
    } else {
      setComponents(mvpBlocksComponents);
    }
    setSelectedCategory('all');
    setLoading(false);
  }, [selectedLibrary]);

  const filteredComponents = components.filter(component => {
    const matchesSearch = component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         component.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || component.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const copyToClipboard = (code) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  return (
    <div className="flex h-full overflow-hidden">
      {/* Sidebar */}
      <div className="w-64 glass border-r border-white/10 flex flex-col">
        <div className="p-4 border-b border-white/10">
          <h2 className="text-xl font-bold bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent">
            Component Library
          </h2>
          <div className="flex gap-2 mt-3">
            <button
              onClick={() => setSelectedLibrary('kibo')}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all ${
                selectedLibrary === 'kibo'
                  ? 'bg-orange-500 text-white'
                  : 'bg-white/10 text-gray-400 hover:text-white hover:bg-white/20'
              }`}
            >
              Kibo UI
            </button>
            <button
              onClick={() => setSelectedLibrary('mvpblocks')}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all ${
                selectedLibrary === 'mvpblocks'
                  ? 'bg-orange-500 text-white'
                  : 'bg-white/10 text-gray-400 hover:text-white hover:bg-white/20'
              }`}
            >
              MVP Blocks
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search components..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-lg bg-white/5 border border-white/10 focus:outline-none focus:border-orange-500/50 text-white placeholder-gray-400"
            />
          </div>
        </div>

        {/* Categories */}
        <div className="flex-1 overflow-y-auto px-2">
          <div className="space-y-1">
            {categories.map(category => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-orange-500/20 to-orange-600/20 text-orange-400'
                      : 'text-gray-300 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{category.name}</span>
                  </div>
                  {category.count && (
                    <span className="text-xs bg-white/10 px-2 py-0.5 rounded-full">
                      {category.count}
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="glass-subtle border-b border-white/10 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-semibold text-white">
              {selectedCategory === 'all' ? 'All Components' : categories.find(c => c.id === selectedCategory)?.name}
            </h1>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setViewMode('preview')}
                className={`px-3 py-1.5 rounded-lg flex items-center gap-2 transition-all ${
                  viewMode === 'preview'
                    ? 'bg-orange-500 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
              >
                <Eye className="w-4 h-4" />
                Preview
              </button>
              <button
                onClick={() => setViewMode('code')}
                className={`px-3 py-1.5 rounded-lg flex items-center gap-2 transition-all ${
                  viewMode === 'code'
                    ? 'bg-orange-500 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
              >
                <Code className="w-4 h-4" />
                Code
              </button>
            </div>
          </div>
        </div>

        {/* Component Grid */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="loading-spinner w-8 h-8" />
            </div>
          ) : filteredComponents.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <Package className="w-12 h-12 mb-4" />
              <p>No components found</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredComponents.map(component => (
                <div
                  key={component.id}
                  className="glass rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-[1.02] cursor-pointer"
                  onClick={() => setSelectedComponent(component)}
                >
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">{component.name}</h3>
                    <p className="text-sm text-gray-400 mb-4">{component.description}</p>
                    
                    {viewMode === 'preview' ? (
                      <div className="bg-black/30 rounded-lg p-6 flex items-center justify-center min-h-[120px]">
                        {selectedLibrary === 'kibo' ? (
                          <SimpleKiboComponent componentId={component.id} showExample={true} />
                        ) : (
                          <div className="text-center">
                            <Blocks className="w-12 h-12 text-orange-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-400">Preview available after installation</p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="relative">
                        <pre className="bg-black/30 rounded-lg p-4 text-xs overflow-x-auto max-h-[200px]">
                          <code className="text-gray-300">
                            {componentExamples[component.id]?.code || `// Example code for ${component.name}`}
                          </code>
                        </pre>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            copyToClipboard(componentExamples[component.id]?.code || '');
                          }}
                          className="absolute top-2 right-2 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all"
                        >
                          {copiedCode === componentExamples[component.id]?.code ? (
                            <Check className="w-4 h-4 text-green-400" />
                          ) : (
                            <Copy className="w-4 h-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Component Detail Modal */}
      {selectedComponent && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4" onClick={() => setSelectedComponent(null)}>
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
          <div className="relative glass bg-gray-900/95 rounded-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden animate-scaleIn" onClick={e => e.stopPropagation()}>
            <div className="border-b border-white/10 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">{selectedComponent.name}</h2>
                  <p className="text-gray-400 mt-1">{selectedComponent.description}</p>
                </div>
                <button
                  onClick={() => setSelectedComponent(null)}
                  className="p-2 rounded-lg hover:bg-white/10 transition-all"
                >
                  <span className="text-2xl text-gray-400">×</span>
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Preview</h3>
                  <div className="bg-black/30 rounded-xl p-8 flex items-center justify-center min-h-[200px]">
                    {selectedLibrary === 'kibo' ? (
                      <SimpleKiboComponent componentId={selectedComponent.id} showExample={true} />
                    ) : (
                      <div className="text-center">
                        <Blocks className="w-16 h-16 text-orange-400 mx-auto mb-3" />
                        <p className="text-gray-400">MVP Blocks component preview</p>
                        <p className="text-sm text-gray-500 mt-2">Install the component to see it in action</p>
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Code</h3>
                  <div className="relative">
                    <pre className="bg-black/30 rounded-xl p-6 overflow-x-auto">
                      <code className="text-gray-300 text-sm">
                        {componentExamples[selectedComponent.id]?.code || `// Example code for ${selectedComponent.name}`}
                      </code>
                    </pre>
                    <button
                      onClick={() => copyToClipboard(componentExamples[selectedComponent.id]?.code || '')}
                      className="absolute top-4 right-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all"
                    >
                      {copiedCode === componentExamples[selectedComponent.id]?.code ? (
                        <Check className="w-4 h-4 text-green-400" />
                      ) : (
                        <Copy className="w-4 h-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Installation</h3>
                  <div className="bg-black/30 rounded-xl p-4">
                    <code className="text-sm text-gray-300">
                      {selectedLibrary === 'kibo' 
                        ? `bunx shadcn@latest add https://kokonutui.com/r/${selectedComponent.id}.json`
                        : `bunx shadcn@latest add https://mvpblocks.com/r/${selectedComponent.id}.json`
                      }
                    </code>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Documentation</h3>
                  <p className="text-gray-400">
                    For full documentation and more examples, visit{' '}
                    <a 
                      href={selectedLibrary === 'kibo' 
                        ? `https://kokonutui.com/docs/components/${selectedComponent.id}`
                        : `https://mvpblocks.com/docs/${selectedComponent.category}/${selectedComponent.id}`
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-orange-400 hover:text-orange-300 underline"
                    >
                      {selectedLibrary === 'kibo' 
                        ? `kokonutui.com/docs/components/${selectedComponent.id}`
                        : `mvpblocks.com/docs/${selectedComponent.category}/${selectedComponent.id}`
                      }
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ComponentViewer;