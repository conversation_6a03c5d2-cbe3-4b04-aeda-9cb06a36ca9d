import React from 'react';
import ReactDOM from 'react-dom/client';
import { createHashRouter, RouterProvider } from 'react-router-dom';
import { HeroUIProvider } from '@heroui/react';
import './index.css';
import App from './App';
import Settings from './pages/Settings';
import { ChatProvider } from './context/ChatContext';
import { ContextManagerProvider } from './context/ContextManagerContext';
import { ThemeProvider } from './components/theme/ThemeProvider';
import AppShell from './components/AppShell';
import Home from './pages/Home';
import Profile from './pages/Profile';
import ManagementPage from './pages/ManagementPage';
import MultidialogPage from './pages/MultidialogPage';
import ComponentViewer from './pages/ComponentViewer';
import ProviderSettings from './pages/ProviderSettings';
import PicaPlayground from './pages/PicaPlayground';
import CuicuiPlayground from './pages/CuicuiPlayground';
import InjaBuilder from './pages/InjaBuilder';
import ThemeEditor from './pages/ThemeEditor';
import DevTools from './pages/DevTools';
import Terminal from './pages/Terminal';
import { initializeTheme } from './themes';

// Initialize theme system
initializeTheme();

const router = createHashRouter([
  {
    path: '/',
    element: <AppShell />,
    children: [
      { path: '/', element: <Home /> },
      { path: '/chat', element: <App /> },
      { path: '/settings', element: <Settings /> },
      { path: '/profile', element: <Profile /> },
      { path: '/management', element: <ManagementPage /> },
      { path: '/multidialog', element: <MultidialogPage /> },
      { path: '/components', element: <ComponentViewer /> },
      { path: '/provider-settings', element: <ProviderSettings /> },
      { path: '/pica-playground', element: <PicaPlayground /> },
      { path: '/cuicui', element: <CuicuiPlayground /> },
      { path: '/inja-builder', element: <InjaBuilder /> },
      { path: '/theme-editor', element: <ThemeEditor /> },
      { path: '/dev-tools', element: <DevTools /> },
      { path: '/terminal', element: <Terminal /> },
    ],
  },
]);

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <HeroUIProvider>
      <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
        <ContextManagerProvider>
          <ChatProvider>
            <RouterProvider router={router} />
          </ChatProvider>
        </ContextManagerProvider>
      </ThemeProvider>
    </HeroUIProvider>
  </React.StrictMode>
); 