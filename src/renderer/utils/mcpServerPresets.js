// Common MCP server presets for easy configuration
export const MCP_SERVER_PRESETS = {
  filesystem: {
    id: 'filesystem',
    name: 'File System',
    description: 'Read, write, and manage files in your local file system',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-filesystem@latest', '/Users/<USER>/Documents'],
      env: {}
    }
  },
  github: {
    id: 'github',
    name: 'GitH<PERSON>',
    description: 'Interact with GitHub repositories, issues, and pull requests',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-github@latest'],
      env: {
        GITHUB_PERSONAL_ACCESS_TOKEN: '<your-github-token>'
      }
    }
  },
  memory: {
    id: 'memory',
    name: 'Memory',
    description: 'Store and retrieve information across conversations',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-memory@latest'],
      env: {}
    }
  },
  time: {
    id: 'time',
    name: 'Time',
    description: 'Get current time, timezone information, and time calculations',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-time@latest'],
      env: {}
    }
  },
  fetch: {
    id: 'fetch',
    name: 'Web Fetch',
    description: 'Fetch and process content from web URLs',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-fetch@latest'],
      env: {}
    }
  },
  sqlite: {
    id: 'sqlite',
    name: 'SQLite',
    description: 'Query and manage SQLite databases',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-sqlite@latest', '--db-path', '<path-to-your-db.sqlite>'],
      env: {}
    }
  },
  postgres: {
    id: 'postgres',
    name: 'PostgreSQL',
    description: 'Query and manage PostgreSQL databases',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-postgres@latest', '<postgres-connection-string>'],
      env: {}
    }
  },
  puppeteer: {
    id: 'puppeteer',
    name: 'Puppeteer',
    description: 'Automate web browser interactions and screenshots',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-puppeteer@latest'],
      env: {}
    }
  },
  // Add the servers you already have available
  'kibo-ui': {
    id: 'kibo-ui',
    name: 'Kibo UI Components',
    description: 'Access Kibo UI component documentation and examples',
    config: {
      transport: 'streamableHttp',
      url: 'https://www.kibo-ui.com/api/mcp/http'
    }
  },
  context7: {
    id: 'context7',
    name: 'Context7 Documentation',
    description: 'Search and retrieve code documentation for various libraries',
    config: {
      transport: 'stdio',
      command: 'npx',
      args: ['-y', '@upstash/context7-mcp@latest'],
      env: {}
    }
  }
};

// Helper function to get preset by ID
export function getMcpPreset(presetId) {
  return MCP_SERVER_PRESETS[presetId];
}

// Helper function to get all preset IDs
export function getMcpPresetIds() {
  return Object.keys(MCP_SERVER_PRESETS);
}

// Helper function to validate and prepare config
export function prepareMcpConfig(preset, customizations = {}) {
  const config = { ...preset.config };
  
  // Apply any customizations
  if (customizations.env) {
    config.env = { ...config.env, ...customizations.env };
  }
  
  if (customizations.args) {
    config.args = customizations.args;
  }
  
  return config;
}