import React, { useEffect, useRef, useState } from 'react';
import { Terminal as XTerminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import 'xterm/css/xterm.css';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { X, Maximize2, Minimize2, Terminal as TerminalIcon } from 'lucide-react';

const Terminal = ({ onClose, isFullscreen, onToggleFullscreen }) => {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const [terminalId, setTerminalId] = useState(null);
  const dataListenerRef = useRef(null);
  const exitListenerRef = useRef(null);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Create terminal instance
    const xterm = new XTerminal({
      theme: {
        background: '#000000',
        foreground: '#ffffff',
        cursor: '#ffffff',
        cursorAccent: '#000000',
        selection: 'rgba(255, 255, 255, 0.3)',
        black: '#000000',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#bd93f9',
        magenta: '#ff79c6',
        cyan: '#8be9fd',
        white: '#bfbfbf',
        brightBlack: '#4d4d4d',
        brightRed: '#ff6e6e',
        brightGreen: '#69ff94',
        brightYellow: '#ffffa5',
        brightBlue: '#d6acff',
        brightMagenta: '#ff92df',
        brightCyan: '#a4ffff',
        brightWhite: '#ffffff'
      },
      fontFamily: 'JetBrains Mono, Menlo, Monaco, "Courier New", monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      allowTransparency: true,
      scrollback: 10000,
    });

    // Add fit addon
    const fitAddon = new FitAddon();
    xterm.loadAddon(fitAddon);
    
    // Add web links addon
    const webLinksAddon = new WebLinksAddon();
    xterm.loadAddon(webLinksAddon);

    // Open terminal in the DOM
    xterm.open(terminalRef.current);
    
    // Store refs
    xtermRef.current = xterm;
    fitAddonRef.current = fitAddon;

    // Fit terminal to container
    fitAddon.fit();

    // Create terminal process
    const createTerminal = async () => {
      const { success, terminalId: id, error } = await window.electron.terminal.create({
        cols: xterm.cols,
        rows: xterm.rows
      });

      if (success && id) {
        setTerminalId(id);

        // Listen for data from terminal
        dataListenerRef.current = window.electron.terminal.onData(({ terminalId: dataId, data }) => {
          if (dataId === id) {
            xterm.write(data);
          }
        });

        // Listen for terminal exit
        exitListenerRef.current = window.electron.terminal.onExit(({ terminalId: exitId }) => {
          if (exitId === id) {
            xterm.write('\r\n[Process terminated]\r\n');
          }
        });

        // Send input to terminal
        xterm.onData((data) => {
          window.electron.terminal.write({ terminalId: id, data });
        });

        // Handle resize
        xterm.onResize(({ cols, rows }) => {
          window.electron.terminal.resize({ terminalId: id, cols, rows });
        });
      } else {
        xterm.write(`\r\n[Error creating terminal: ${error || 'Unknown error'}]\r\n`);
      }
    };

    createTerminal();

    // Handle window resize
    const handleResize = () => {
      if (fitAddonRef.current) {
        fitAddonRef.current.fit();
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (dataListenerRef.current) {
        dataListenerRef.current();
      }
      
      if (exitListenerRef.current) {
        exitListenerRef.current();
      }
      
      if (terminalId) {
        window.electron.terminal.kill({ terminalId });
      }
      
      if (xtermRef.current) {
        xtermRef.current.dispose();
      }
    };
  }, []);

  // Handle fullscreen toggle
  useEffect(() => {
    if (fitAddonRef.current) {
      // Small delay to let CSS transitions complete
      setTimeout(() => {
        fitAddonRef.current.fit();
      }, 300);
    }
  }, [isFullscreen]);

  return (
    <Card className={`flex flex-col bg-black border-gray-800 ${
      isFullscreen ? 'fixed inset-4 z-50' : 'h-96'
    } transition-all duration-300`}>
      {/* Terminal Header */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-900 border-b border-gray-800">
        <div className="flex items-center gap-2">
          <TerminalIcon className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium text-gray-300">Terminal</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 text-gray-400 hover:text-white"
            onClick={onToggleFullscreen}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 text-gray-400 hover:text-white"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      <div ref={terminalRef} className="flex-1 p-2" />
    </Card>
  );
};

export default Terminal;