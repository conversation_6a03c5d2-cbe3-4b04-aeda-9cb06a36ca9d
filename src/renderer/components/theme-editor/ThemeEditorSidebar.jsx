import React, { useState } from 'react';
import { ScrollArea } from '../ui/scroll-area';
import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Card } from '../ui/card';
import { 
  Palette, Type, Layout, Layers, Package, 
  ChevronRight, ChevronDown 
} from 'lucide-react';

const ThemeEditorSidebar = ({ 
  currentMode,
  themeStyles,
  updateThemeVariable,
  hslAdjustments,
  setHslAdjustments,
  activeComponent,
  setActiveComponent
}) => {
  const [expandedSections, setExpandedSections] = useState({
    colors: true,
    typography: false,
    layout: false,
    components: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Component categories for quick navigation
  const componentCategories = [
    {
      name: 'Buttons',
      icon: Package,
      components: ['button', 'button-variants', 'button-sizes']
    },
    {
      name: 'Form Controls',
      icon: Layout,
      components: ['input', 'select', 'checkbox', 'slider', 'textarea']
    },
    {
      name: 'Cards',
      icon: Layers,
      components: ['card', 'card-interactive', 'card-stats']
    },
    {
      name: 'Feedback',
      icon: Package,
      components: ['alert', 'badge', 'dialog', 'toast']
    },
    {
      name: 'Navigation',
      icon: Layout,
      components: ['tabs', 'breadcrumb', 'pagination']
    }
  ];

  // Color variable groups
  const colorGroups = [
    {
      name: 'Primary',
      vars: ['--primary', '--primary-foreground']
    },
    {
      name: 'Secondary',
      vars: ['--secondary', '--secondary-foreground']
    },
    {
      name: 'Accent',
      vars: ['--accent', '--accent-foreground']
    },
    {
      name: 'Destructive',
      vars: ['--destructive', '--destructive-foreground']
    },
    {
      name: 'Background',
      vars: ['--background', '--foreground', '--muted', '--muted-foreground']
    },
    {
      name: 'UI Elements',
      vars: ['--card', '--card-foreground', '--popover', '--popover-foreground']
    },
    {
      name: 'Borders',
      vars: ['--border', '--input', '--ring']
    }
  ];

  const ColorInput = ({ varName, value }) => {
    const [h, s, l] = value.split(' ').map(v => parseFloat(v));
    
    return (
      <div className="space-y-2">
        <Label className="text-xs">{varName.replace('--', '')}</Label>
        <div className="flex items-center gap-2">
          <div 
            className="w-10 h-10 rounded border border-border"
            style={{ backgroundColor: `hsl(${h}, ${s}%, ${l}%)` }}
          />
          <div className="flex-1 space-y-1">
            <input
              type="range"
              min="0"
              max="360"
              value={h}
              onChange={(e) => updateThemeVariable(varName, `${e.target.value} ${s}% ${l}%`)}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex gap-1">
              <input
                type="range"
                min="0"
                max="100"
                value={s}
                onChange={(e) => updateThemeVariable(varName, `${h} ${e.target.value}% ${l}%`)}
                className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              <input
                type="range"
                min="0"
                max="100"
                value={l}
                onChange={(e) => updateThemeVariable(varName, `${h} ${s}% ${e.target.value}%`)}
                className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-80 bg-card border-r border-border h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h3 className="font-semibold">Theme Customization</h3>
        <p className="text-sm text-muted-foreground">Edit colors and styles</p>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="styles" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 p-1 m-2">
          <TabsTrigger value="styles">Styles</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
        </TabsList>

        {/* Styles Tab */}
        <TabsContent value="styles" className="flex-1 overflow-hidden m-0">
          <ScrollArea className="h-full">
            <div className="p-4 space-y-4">
              {/* Colors Section */}
              <div>
                <button
                  onClick={() => toggleSection('colors')}
                  className="flex items-center justify-between w-full p-2 hover:bg-accent/50 rounded"
                >
                  <div className="flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    <span className="font-medium">Colors</span>
                  </div>
                  {expandedSections.colors ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </button>
                
                {expandedSections.colors && (
                  <div className="mt-2 space-y-4">
                    {colorGroups.map(group => (
                      <div key={group.name} className="space-y-2">
                        <h4 className="text-sm font-medium text-muted-foreground">{group.name}</h4>
                        {group.vars.map(varName => (
                          <ColorInput
                            key={varName}
                            varName={varName}
                            value={themeStyles[currentMode][varName]}
                          />
                        ))}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Typography Section */}
              <div>
                <button
                  onClick={() => toggleSection('typography')}
                  className="flex items-center justify-between w-full p-2 hover:bg-accent/50 rounded"
                >
                  <div className="flex items-center gap-2">
                    <Type className="h-4 w-4" />
                    <span className="font-medium">Typography</span>
                  </div>
                  {expandedSections.typography ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </button>
                
                {expandedSections.typography && (
                  <div className="mt-2 space-y-3">
                    <div>
                      <Label className="text-xs">Font Sans</Label>
                      <select 
                        className="w-full p-2 bg-background border border-border rounded text-sm"
                        value={themeStyles[currentMode]['--font-sans']}
                        onChange={(e) => updateThemeVariable('--font-sans', e.target.value)}
                      >
                        <option value="Inter, system-ui, sans-serif">Inter</option>
                        <option value="Roboto, sans-serif">Roboto</option>
                        <option value="Open Sans, sans-serif">Open Sans</option>
                        <option value="Montserrat, sans-serif">Montserrat</option>
                      </select>
                    </div>
                  </div>
                )}
              </div>

              {/* Layout Section */}
              <div>
                <button
                  onClick={() => toggleSection('layout')}
                  className="flex items-center justify-between w-full p-2 hover:bg-accent/50 rounded"
                >
                  <div className="flex items-center gap-2">
                    <Layout className="h-4 w-4" />
                    <span className="font-medium">Layout</span>
                  </div>
                  {expandedSections.layout ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </button>
                
                {expandedSections.layout && (
                  <div className="mt-2 space-y-3">
                    <div>
                      <Label className="text-xs">Border Radius</Label>
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={parseFloat(themeStyles[currentMode]['--radius'])}
                        onChange={(e) => updateThemeVariable('--radius', `${e.target.value}rem`)}
                        className="w-full"
                      />
                      <span className="text-xs text-muted-foreground">{themeStyles[currentMode]['--radius']}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>
        </TabsContent>

        {/* Components Tab */}
        <TabsContent value="components" className="flex-1 overflow-hidden m-0">
          <ScrollArea className="h-full">
            <div className="p-4 space-y-2">
              {componentCategories.map(category => (
                <Card 
                  key={category.name}
                  className="p-3 cursor-pointer hover:bg-accent/50 transition-colors"
                  onClick={() => setActiveComponent(category.components[0])}
                >
                  <div className="flex items-center gap-2">
                    <category.icon className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <h4 className="text-sm font-medium">{category.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        {category.components.length} components
                      </p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ThemeEditorSidebar;