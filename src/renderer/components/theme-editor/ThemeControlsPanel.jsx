import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Slider } from '../ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Input } from '../ui/input';
import { 
  Palette, Type, Layout, Sparkles, ChevronUp, ChevronDown,
  Sun, Moon, Monitor, Settings2, Brush, Shapes
} from 'lucide-react';

const ThemeControlsPanel = ({
  currentMode,
  setCurrentMode,
  themeStyles,
  updateThemeVariable,
  hslAdjustments,
  setHslAdjustments,
  isExpanded,
  setIsExpanded
}) => {
  const [activeTab, setActiveTab] = useState('colors');

  // Color groups for organization
  const colorGroups = {
    primary: {
      title: 'Primary & Secondary',
      icon: Palette,
      colors: [
        { name: 'Primary', var: '--primary' },
        { name: 'Primary Text', var: '--primary-foreground' },
        { name: 'Secondary', var: '--secondary' },
        { name: 'Secondary Text', var: '--secondary-foreground' },
      ]
    },
    accent: {
      title: 'Accent & Muted',
      icon: Brush,
      colors: [
        { name: 'Accent', var: '--accent' },
        { name: 'Accent Text', var: '--accent-foreground' },
        { name: 'Muted', var: '--muted' },
        { name: 'Muted Text', var: '--muted-foreground' },
      ]
    },
    background: {
      title: 'Background & Foreground',
      icon: Monitor,
      colors: [
        { name: 'Background', var: '--background' },
        { name: 'Foreground', var: '--foreground' },
        { name: 'Card', var: '--card' },
        { name: 'Card Text', var: '--card-foreground' },
      ]
    },
    ui: {
      title: 'UI Elements',
      icon: Shapes,
      colors: [
        { name: 'Border', var: '--border' },
        { name: 'Input', var: '--input' },
        { name: 'Ring', var: '--ring' },
        { name: 'Destructive', var: '--destructive' },
      ]
    }
  };

  const ColorControl = ({ name, varName, value }) => {
    const [h, s, l] = value.split(' ').map(v => parseFloat(v));
    
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-xs font-medium">{name}</Label>
          <div className="flex items-center gap-2">
            <div 
              className="w-8 h-8 rounded border border-border shadow-sm"
              style={{ backgroundColor: `hsl(${h}, ${s}%, ${l}%)` }}
            />
            <Input
              type="text"
              value={`${Math.round(h)} ${Math.round(s)}% ${Math.round(l)}%`}
              onChange={(e) => updateThemeVariable(varName, e.target.value)}
              className="w-32 h-8 text-xs font-mono"
            />
          </div>
        </div>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <span className="text-xs w-8">H</span>
            <Slider
              value={[h]}
              onValueChange={([v]) => updateThemeVariable(varName, `${v} ${s}% ${l}%`)}
              max={360}
              step={1}
              className="flex-1"
            />
            <span className="text-xs w-10 text-right">{Math.round(h)}°</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs w-8">S</span>
            <Slider
              value={[s]}
              onValueChange={([v]) => updateThemeVariable(varName, `${h} ${v}% ${l}%`)}
              max={100}
              step={1}
              className="flex-1"
            />
            <span className="text-xs w-10 text-right">{Math.round(s)}%</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs w-8">L</span>
            <Slider
              value={[l]}
              onValueChange={([v]) => updateThemeVariable(varName, `${h} ${s}% ${v}%`)}
              max={100}
              step={1}
              className="flex-1"
            />
            <span className="text-xs w-10 text-right">{Math.round(l)}%</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`fixed bottom-0 left-0 right-0 bg-background border-t border-border transition-all duration-300 ${
      isExpanded ? 'h-[400px]' : 'h-12'
    }`}>
      {/* Toggle Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="absolute -top-10 left-1/2 -translate-x-1/2 bg-card border border-border rounded-t-lg px-6 py-2 flex items-center gap-2 hover:bg-accent/10 transition-colors"
      >
        <Settings2 className="w-4 h-4" />
        Theme Controls
        {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
      </button>

      {/* Controls Content */}
      {isExpanded && (
        <div className="h-full overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <div className="flex items-center justify-between px-4 py-2 border-b border-border">
              <TabsList>
                <TabsTrigger value="colors" className="flex items-center gap-2">
                  <Palette className="w-4 h-4" />
                  Colors
                </TabsTrigger>
                <TabsTrigger value="typography" className="flex items-center gap-2">
                  <Type className="w-4 h-4" />
                  Typography
                </TabsTrigger>
                <TabsTrigger value="layout" className="flex items-center gap-2">
                  <Layout className="w-4 h-4" />
                  Layout
                </TabsTrigger>
                <TabsTrigger value="adjustments" className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4" />
                  Adjustments
                </TabsTrigger>
              </TabsList>

              {/* Mode Switcher */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant={currentMode === 'light' ? 'default' : 'outline'}
                  onClick={() => setCurrentMode('light')}
                  className="h-8"
                >
                  <Sun className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant={currentMode === 'dark' ? 'default' : 'outline'}
                  onClick={() => setCurrentMode('dark')}
                  className="h-8"
                >
                  <Moon className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Colors Tab */}
            <TabsContent value="colors" className="h-[calc(100%-60px)] overflow-y-auto p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(colorGroups).map(([key, group]) => (
                  <Card key={key} className="h-fit">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <group.icon className="w-4 h-4" />
                        {group.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {group.colors.map(color => (
                        <ColorControl
                          key={color.var}
                          name={color.name}
                          varName={color.var}
                          value={themeStyles[currentMode][color.var]}
                        />
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Typography Tab */}
            <TabsContent value="typography" className="h-[calc(100%-60px)] overflow-y-auto p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Font Families</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-xs">Sans Serif</Label>
                      <Select 
                        value={themeStyles[currentMode]['--font-sans']}
                        onValueChange={(value) => updateThemeVariable('--font-sans', value)}
                      >
                        <SelectTrigger className="h-9">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Inter, system-ui, sans-serif">Inter</SelectItem>
                          <SelectItem value="Roboto, sans-serif">Roboto</SelectItem>
                          <SelectItem value="Open Sans, sans-serif">Open Sans</SelectItem>
                          <SelectItem value="Lato, sans-serif">Lato</SelectItem>
                          <SelectItem value="Montserrat, sans-serif">Montserrat</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-xs">Serif</Label>
                      <Select 
                        value={themeStyles[currentMode]['--font-serif']}
                        onValueChange={(value) => updateThemeVariable('--font-serif', value)}
                      >
                        <SelectTrigger className="h-9">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Georgia, serif">Georgia</SelectItem>
                          <SelectItem value="Times New Roman, serif">Times New Roman</SelectItem>
                          <SelectItem value="Playfair Display, serif">Playfair Display</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-xs">Monospace</Label>
                      <Select 
                        value={themeStyles[currentMode]['--font-mono']}
                        onValueChange={(value) => updateThemeVariable('--font-mono', value)}
                      >
                        <SelectTrigger className="h-9">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Menlo, monospace">Menlo</SelectItem>
                          <SelectItem value="Monaco, monospace">Monaco</SelectItem>
                          <SelectItem value="Fira Code, monospace">Fira Code</SelectItem>
                          <SelectItem value="JetBrains Mono, monospace">JetBrains Mono</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Typography Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Letter Spacing</Label>
                        <span className="text-xs text-muted-foreground">{themeStyles[currentMode]['--letter-spacing']}</span>
                      </div>
                      <Slider
                        value={[parseFloat(themeStyles[currentMode]['--letter-spacing'])]}
                        onValueChange={([v]) => updateThemeVariable('--letter-spacing', v + 'em')}
                        min={-0.1}
                        max={0.2}
                        step={0.01}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Layout Tab */}
            <TabsContent value="layout" className="h-[calc(100%-60px)] overflow-y-auto p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Spacing & Radius</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Border Radius</Label>
                        <span className="text-xs text-muted-foreground">{themeStyles[currentMode]['--radius']}</span>
                      </div>
                      <Slider
                        value={[parseFloat(themeStyles[currentMode]['--radius'])]}
                        onValueChange={([v]) => updateThemeVariable('--radius', v + 'rem')}
                        min={0}
                        max={2}
                        step={0.1}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Spacing Unit</Label>
                        <span className="text-xs text-muted-foreground">{themeStyles[currentMode]['--spacing']}</span>
                      </div>
                      <Slider
                        value={[parseFloat(themeStyles[currentMode]['--spacing'])]}
                        onValueChange={([v]) => updateThemeVariable('--spacing', v + 'rem')}
                        min={0.1}
                        max={0.5}
                        step={0.05}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Shadow Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Shadow Opacity</Label>
                        <span className="text-xs text-muted-foreground">{themeStyles[currentMode]['--shadow-opacity']}</span>
                      </div>
                      <Slider
                        value={[parseFloat(themeStyles[currentMode]['--shadow-opacity'])]}
                        onValueChange={([v]) => updateThemeVariable('--shadow-opacity', v)}
                        min={0}
                        max={1}
                        step={0.1}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Shadow Blur</Label>
                        <span className="text-xs text-muted-foreground">{themeStyles[currentMode]['--shadow-blur']}</span>
                      </div>
                      <Slider
                        value={[parseFloat(themeStyles[currentMode]['--shadow-blur'])]}
                        onValueChange={([v]) => updateThemeVariable('--shadow-blur', v + 'px')}
                        min={0}
                        max={50}
                        step={5}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Adjustments Tab */}
            <TabsContent value="adjustments" className="h-[calc(100%-60px)] overflow-y-auto p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Global HSL Adjustments</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Hue Shift</Label>
                        <span className="text-xs text-muted-foreground">{hslAdjustments.hueShift}°</span>
                      </div>
                      <Slider
                        value={[hslAdjustments.hueShift]}
                        onValueChange={([v]) => setHslAdjustments(prev => ({ ...prev, hueShift: v }))}
                        min={-180}
                        max={180}
                        step={10}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Saturation Scale</Label>
                        <span className="text-xs text-muted-foreground">{hslAdjustments.saturationScale}x</span>
                      </div>
                      <Slider
                        value={[hslAdjustments.saturationScale]}
                        onValueChange={([v]) => setHslAdjustments(prev => ({ ...prev, saturationScale: v }))}
                        min={0}
                        max={2}
                        step={0.1}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Lightness Scale</Label>
                        <span className="text-xs text-muted-foreground">{hslAdjustments.lightnessScale}x</span>
                      </div>
                      <Slider
                        value={[hslAdjustments.lightnessScale]}
                        onValueChange={([v]) => setHslAdjustments(prev => ({ ...prev, lightnessScale: v }))}
                        min={0.5}
                        max={1.5}
                        step={0.1}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="text-sm">Quick Presets</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-4 gap-2">
                      {[
                        { name: 'Default', h: 0, s: 1, l: 1 },
                        { name: 'Warm', h: 30, s: 1.2, l: 1.1 },
                        { name: 'Cool', h: -30, s: 1.1, l: 0.95 },
                        { name: 'Vibrant', h: 0, s: 1.5, l: 1 },
                        { name: 'Muted', h: 0, s: 0.7, l: 1 },
                        { name: 'Dark', h: 0, s: 1, l: 0.8 },
                        { name: 'Light', h: 0, s: 1, l: 1.2 },
                        { name: 'Monochrome', h: 0, s: 0, l: 1 },
                      ].map(preset => (
                        <Button
                          key={preset.name}
                          variant="outline"
                          size="sm"
                          onClick={() => setHslAdjustments({
                            hueShift: preset.h,
                            saturationScale: preset.s,
                            lightnessScale: preset.l
                          })}
                          className="h-8"
                        >
                          {preset.name}
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
};

export default ThemeControlsPanel;