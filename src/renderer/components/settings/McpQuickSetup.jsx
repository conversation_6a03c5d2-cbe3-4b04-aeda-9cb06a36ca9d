import React, { useState } from 'react';
import { MCP_SERVER_PRESETS } from '../../utils/mcpServerPresets';

function McpQuickSetup({ existingServers, onAddPreset }) {
  const [selectedPreset, setSelectedPreset] = useState('');
  const [showCustomization, setShowCustomization] = useState(false);
  const [customEnv, setCustomEnv] = useState({});

  const handlePresetSelect = (e) => {
    const presetId = e.target.value;
    setSelectedPreset(presetId);
    
    if (presetId) {
      const preset = MCP_SERVER_PRESETS[presetId];
      // Check if this preset has env variables that need configuration
      const hasConfigurableEnv = preset.config.env && 
        Object.values(preset.config.env).some(val => val.includes('<') && val.includes('>'));
      
      setShowCustomization(hasConfigurableEnv);
      
      // Initialize custom env with placeholder values
      if (hasConfigurableEnv) {
        const envDefaults = {};
        Object.entries(preset.config.env).forEach(([key, value]) => {
          if (value.includes('<') && value.includes('>')) {
            envDefaults[key] = '';
          }
        });
        setCustomEnv(envDefaults);
      }
    } else {
      setShowCustomization(false);
      setCustomEnv({});
    }
  };

  const handleEnvChange = (key, value) => {
    setCustomEnv(prev => ({ ...prev, [key]: value }));
  };

  const handleAddPreset = () => {
    if (!selectedPreset) return;
    
    const preset = MCP_SERVER_PRESETS[selectedPreset];
    const finalConfig = { ...preset.config };
    
    // Apply custom environment variables
    if (Object.keys(customEnv).length > 0) {
      finalConfig.env = { ...finalConfig.env, ...customEnv };
    }
    
    // Check for path placeholders in args
    if (finalConfig.args) {
      finalConfig.args = finalConfig.args.map(arg => {
        if (arg.includes('<') && arg.includes('>')) {
          const replacement = prompt(`Please enter value for: ${arg}`);
          return replacement || arg;
        }
        return arg;
      });
    }
    
    onAddPreset(preset.id, finalConfig);
    
    // Reset form
    setSelectedPreset('');
    setShowCustomization(false);
    setCustomEnv({});
  };

  // Filter out already configured servers
  const availablePresets = Object.entries(MCP_SERVER_PRESETS)
    .filter(([id]) => !existingServers || !existingServers[id]);

  if (availablePresets.length === 0) {
    return null;
  }

  return (
    <div className="mb-6 p-4 bg-black/50 rounded-lg border border-white/10">
      <h4 className="text-sm font-medium text-gray-300 mb-3">Quick Setup - Add Popular MCP Servers</h4>
      
      <div className="space-y-3">
        <select
          value={selectedPreset}
          onChange={handlePresetSelect}
          className="w-full px-3 py-2 border border-gray-500 rounded-md bg-transparent text-white text-sm"
        >
          <option value="">Select a preset server...</option>
          {availablePresets.map(([id, preset]) => (
            <option key={id} value={id}>
              {preset.name} - {preset.description}
            </option>
          ))}
        </select>

        {showCustomization && selectedPreset && (
          <div className="mt-3 p-3 bg-gray-800 rounded-md">
            <p className="text-xs text-gray-400 mb-2">Configure required settings:</p>
            {Object.entries(MCP_SERVER_PRESETS[selectedPreset].config.env).map(([key, defaultValue]) => {
              if (defaultValue.includes('<') && defaultValue.includes('>')) {
                return (
                  <div key={key} className="mb-2">
                    <label className="block text-xs text-gray-300 mb-1">{key}:</label>
                    <input
                      type="text"
                      value={customEnv[key] || ''}
                      onChange={(e) => handleEnvChange(key, e.target.value)}
                      placeholder={defaultValue}
                      className="w-full px-2 py-1 text-sm border border-gray-600 rounded bg-gray-900 text-white"
                    />
                  </div>
                );
              }
              return null;
            })}
          </div>
        )}

        {selectedPreset && (
          <div className="flex items-center gap-2">
            <button
              onClick={handleAddPreset}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors"
            >
              Add {MCP_SERVER_PRESETS[selectedPreset].name}
            </button>
            <button
              onClick={() => {
                setSelectedPreset('');
                setShowCustomization(false);
                setCustomEnv({});
              }}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors"
            >
              Cancel
            </button>
          </div>
        )}
      </div>

      <div className="mt-3 text-xs text-gray-500">
        These presets use npx to run the latest versions. The servers will be automatically started when you launch the app.
      </div>
    </div>
  );
}

export default McpQuickSetup;