version: '3.8'

services:
  cerebras-app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: cerebras-desktop-app-dev
    
    # Port mappings for development
    ports:
      - "7774:5173"  # Vite dev server
      - "7773:7773"  # Electron debug port
    
    # Load environment from .env file
    env_file:
      - .env
    
    # Additional environment variables
    environment:
      - DISPLAY=${DISPLAY:-:99}
      - NODE_ENV=development
      - ELECTRON_DISABLE_SECURITY_WARNINGS=true
      - ELECTRON_NO_SANDBOX=1
      - VITE_PORT=5173
      - ELECTRON_DEBUG_PORT=7773
    
    # Volumes for development
    volumes:
      # Application data
      - cerebras-dev-data:/home/<USER>/.config
      - cerebras-dev-cache:/home/<USER>/.cache
      # Source code for hot reload
      - ./src:/app/src
      - ./electron:/app/electron
      - ./shared:/app/shared
      - ./index.html:/app/index.html
      - ./vite.config.cjs:/app/vite.config.cjs
      - ./tailwind.config.cjs:/app/tailwind.config.cjs
      - ./postcss.config.cjs:/app/postcss.config.cjs
      # For X11 forwarding on Linux
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    
    # Network mode for X11 (Linux)
    network_mode: host
    
    # Security options
    security_opt:
      - seccomp:unconfined
    
    # Restart policy
    restart: unless-stopped
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

volumes:
  cerebras-dev-data:
    driver: local
  cerebras-dev-cache:
    driver: local